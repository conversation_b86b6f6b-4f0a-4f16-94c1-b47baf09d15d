using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfCompanyDal : EfEntityRepositoryBase<Company, GymContext>, ICompanyDal
    {
        // Constructor injection (Scalability için)
        public EfCompanyDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfCompanyDal() : base()
        {
        }

        public List<ActiveCompanyDetailDto> GetActiveCompanies()
        {
            // DI kullanılıyor - Scalability optimized
            var result = from c in _context.Companies
                         where c.IsActive == true
                         select new ActiveCompanyDetailDto
                         {
                           CompanyID = c.CompanyID,
                           CompanyName = c.CompanyName,
                           PhoneNumber = c.PhoneNumber,
                           IsActive = c.IsActive,

                         };
            return result.ToList();
        }
    }
}
